import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useSubscriptionAccess } from '@/hooks/useSubscriptionAccess';
import { supabase } from '@/lib/supabase';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { useIsFocused } from '@react-navigation/native';
import { router, useFocusEffect } from 'expo-router';
import { useCallback, useEffect, useRef, useState } from 'react';
import { ActivityIndicator, Dimensions, FlatList, RefreshControl, StyleSheet, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// Define Transaction interface
interface Transaction {
  id: string;
  type: 'income' | 'expense';
  amount: number;
  description: string;
  transaction_date: string;
  profile_id?: string;
  paid_to_received_from?: string;
  linked_transaction_id?: string;
  counterparty_user_id?: string;
  is_linked?: boolean;
  created_at?: string;
  updated_at?: string;
  effective_linked_id?: string;
}

// Define filter type
type FilterType = 'all' | 'income' | 'expense';
type SortOption = 'newest' | 'oldest' | 'highest' | 'lowest';

const { width, height } = Dimensions.get('window');

// Subscription popup styles
const subscriptionStyles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  popup: {
    width: Math.min(width - 40, 400),
    maxHeight: height * 0.8,
    borderRadius: 20,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  description: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  featuresList: {
    width: '100%',
    marginBottom: 32,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  featureText: {
    fontSize: 14,
    marginLeft: 12,
    flex: 1,
  },
  buttonContainer: {
    width: '100%',
    gap: 12,
  },
  subscribeButton: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
  },
  subscribeButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  cancelButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
    borderWidth: 1,
  },
  cancelButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default function TransactionsScreen() {
  const insets = useSafeAreaInsets();
  const colorScheme = useColorScheme();
  const primaryColor = Colors[colorScheme ?? 'light'].primary;
  const isFocused = useIsFocused();
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [filter, setFilter] = useState<FilterType>('all');
  const [sortBy, setSortBy] = useState<SortOption>('newest');
  const [showSubscriptionPopup, setShowSubscriptionPopup] = useState(false);
  const [lastRefreshTime, setLastRefreshTime] = useState(0);
  const [summary, setSummary] = useState({
    totalIncome: 0,
    totalExpense: 0,
    balance: 0
  });
  
  const subscriptionRef = useRef<any>(null);
  const refreshTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  
  // Use the new subscription access hook
  const { 
    isSubscribed, 
    isExpired, 
    loading: subscriptionLoading,
    refreshSubscription 
  } = useSubscriptionAccess();

  // Show access denied message for expired subscriptions
  const renderAccessDenied = () => (
    <ThemedView style={styles.accessDeniedContainer}>
      <Ionicons 
        name="lock-closed" 
        size={60} 
        color={colorScheme === 'dark' ? '#ef4444' : '#dc2626'} 
      />
      <ThemedText style={styles.accessDeniedTitle}>
        Access Restricted
      </ThemedText>
      <ThemedText style={styles.accessDeniedText}>
        {isExpired() ? 
          'Your subscription has expired. Please renew your subscription to access Transactions.' :
          'You need an active subscription to access Transactions.'
        }
      </ThemedText>
      <TouchableOpacity
        style={[styles.subscribeButton, { 
          backgroundColor: primaryColor
        }]}
        onPress={() => router.push('/(tabs)/subscription')}
      >
        <ThemedText style={[styles.subscribeButtonText, { color: 'white' }]}>
          {isExpired() ? 'Renew Subscription' : 'Get Subscription'}
        </ThemedText>
      </TouchableOpacity>
    </ThemedView>
  );

  // Fetch transactions when screen gets focus and user has access
  useFocusEffect(
    useCallback(() => {
      if (isSubscribed()) {
        fetchTransactions();
        setupRealtimeSubscription();
      }
      
      return () => {
        // Cleanup subscription when screen loses focus
        if (subscriptionRef.current) {
          subscriptionRef.current.unsubscribe();
        }
      };
    }, [isSubscribed])
  );

  // Initial subscription check when screen focuses - only once per focus
  useEffect(() => {
    if (isFocused) {
      console.log("Screen focused - checking if subscription refresh needed");
      refreshSubscription();
    } else {
      // Hide popup when screen loses focus (user navigates away)
      if (showSubscriptionPopup) {
        setShowSubscriptionPopup(false);
      }
    }
  }, [isFocused]); // Removed debouncedRefreshSubscription from deps to prevent loops

  // Show/hide popup based on subscription status - simplified logic
  useEffect(() => {
    console.log("Transactions - Popup effect triggered:", { subscriptionLoading, isFocused });
    
    if (!subscriptionLoading && isFocused) { // Only show popup when screen is focused
      const shouldShowPopup = !isSubscribed();
      console.log("Popup visibility check:", { shouldShowPopup, isSubscribed: isSubscribed(), loading: subscriptionLoading, isFocused });
      
              // FORCE HIDE popup if user has subscription access
        if (isSubscribed()) {
          console.log("Transactions - FORCE HIDING popup: User has access");
          setShowSubscriptionPopup(false);
          return;
        }
      
      // Show popup if user doesn't have access
      if (shouldShowPopup && !showSubscriptionPopup) {
        // Add a small delay to prevent rapid toggling
        setTimeout(() => {
          setShowSubscriptionPopup(true);
        }, 200);
      }
    }
  }, [isSubscribed, subscriptionLoading, isFocused, showSubscriptionPopup]); // Added all relevant deps

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, []);

  useEffect(() => {
    calculateSummary();
  }, [transactions]);

  const setupRealtimeSubscription = async () => {
    // Clean up any existing subscription
    if (subscriptionRef.current) {
      subscriptionRef.current.unsubscribe();
      subscriptionRef.current = null;
    }

    const { data: userData } = await supabase.auth.getUser();
    if (!userData?.user?.id) return;

    // Create a unique channel name to prevent conflicts
    const channelName = `user-transactions-${userData.user.id}-${Date.now()}`;

    // Create a subscription that listens for both transactions AND subscription changes
    subscriptionRef.current = supabase
      .channel(channelName)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'transactions',
        filter: `user_id=eq.${userData.user.id}`
      }, (payload) => {
        console.log('Transaction change detected:', payload.eventType);
        fetchTransactions();
      })
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'user_subscriptions',
        filter: `user_id=eq.${userData.user.id}`
      }, (payload) => {
        console.log('User subscription changed:', payload);
        // Refresh subscription status immediately for instant access
        if (refreshSubscription) {
          refreshSubscription();
        }
        // Also fetch transactions if user now has access
        setTimeout(() => {
          if (isSubscribed()) {
            console.log("Subscription activated, fetching transactions");
            fetchTransactions();
          }
        }, 500);
      })
      .subscribe((status) => {
        console.log('Realtime subscription status:', status);
      });
  };

  const fetchTransactions = async () => {
    try {
      setLoading(true);
      const { data: userData } = await supabase.auth.getUser();
      
      if (!userData?.user?.id) {
        setTransactions([]);
        return;
      }
      
      // First get the user's profile ID
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('id')
        .eq('user_id', userData.user.id)
        .single();
        
      if (profileError) {
        console.error('Error fetching user profile:', profileError);
        setTransactions([]);
        return;
      }
      
      // Now fetch transactions only for the current user (no linked transactions from others)
      // This ensures we only see transactions owned by this user
      const { data, error } = await supabase
        .from('transactions')
        .select(`
          id, 
          type, 
          amount, 
          description, 
          transaction_date, 
          profile_id, 
          paid_to_received_from, 
          linked_transaction_id, 
          counterparty_user_id,
          created_at,
          updated_at
        `)
        .eq('user_id', userData.user.id)
        .order('transaction_date', { ascending: false });

      if (error) {
        console.error('Error fetching transactions:', error);
        throw error;
      }
      
      // Transform data to add derived fields expected by the UI
      const transformedData = (data || []).map(transaction => {
        const isLinked = transaction.linked_transaction_id || transaction.counterparty_user_id;
        
        return {
          ...transaction,
          is_linked: !!isLinked,
          effective_linked_id: transaction.linked_transaction_id
        };
      });
      
      // Sort all transactions by date (newest first)
      transformedData.sort((a, b) => {
        // First compare by transaction_date
        const dateA = new Date(a.transaction_date).getTime();
        const dateB = new Date(b.transaction_date).getTime();
        
        if (dateA !== dateB) {
          return dateB - dateA; // newest first by transaction date
        }
        
        // If dates are the same, use created_at as a tiebreaker
        const createdA = new Date(a.created_at || 0).getTime();
        const createdB = new Date(b.created_at || 0).getTime();
        return createdB - createdA; // newest created first
      });
      
      setTransactions(transformedData);
    } catch (error) {
      console.error('Error fetching transactions:', error);
      // Fallback to empty array if error
      setTransactions([]);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = useCallback(() => {
    if (isSubscribed()) {
      setRefreshing(true);
      fetchTransactions();
    }
    // Refresh subscription status
    if (refreshSubscription) {
      refreshSubscription();
    }
  }, [isSubscribed, refreshSubscription]);

  const calculateSummary = () => {
    const totalIncome = transactions
      .filter(t => t.type === 'income')
      .reduce((sum, t) => sum + (parseFloat(t.amount.toString()) || 0), 0);
    
    const totalExpense = transactions
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + (parseFloat(t.amount.toString()) || 0), 0);
    
    setSummary({
      totalIncome,
      totalExpense,
      balance: totalIncome - totalExpense
    });
  };

  // Filter and sort transactions
  const getFilteredAndSortedTransactions = () => {
    let result = [...transactions];
    
    // Apply type filter
    if (filter !== 'all') {
      result = result.filter(t => t.type === filter);
    }
    
    // Apply sorting
    result.sort((a, b) => {
      const amountA = parseFloat(a.amount.toString());
      const amountB = parseFloat(b.amount.toString());
      const dateA = new Date(a.transaction_date).getTime();
      const dateB = new Date(b.transaction_date).getTime();
      
      switch (sortBy) {
        case 'newest':
          if (dateA !== dateB) {
            return dateB - dateA;
          }
          // If dates are the same, use created_at as a tiebreaker
          const createdA = new Date(a.created_at || 0).getTime();
          const createdB = new Date(b.created_at || 0).getTime();
          return createdB - createdA;
        case 'oldest':
          if (dateA !== dateB) {
            return dateA - dateB;
          }
          // If dates are the same, use created_at as a tiebreaker
          const createdAOld = new Date(a.created_at || 0).getTime();
          const createdBOld = new Date(b.created_at || 0).getTime();
          return createdAOld - createdBOld;
        case 'highest':
          return amountB - amountA;
        case 'lowest':
          return amountA - amountB;
        default:
          return dateB - dateA;
      }
    });
    
    return result;
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString(undefined, {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (error) {
      return dateString;
    }
  };

  const renderTransaction = ({ item }: { item: Transaction }) => {
    // Check if this is a linked transaction - use effective_linked_id if available
    const isLinked = item.is_linked || 
                    item.linked_transaction_id || 
                    item.counterparty_user_id;
    
    return (
      <ThemedView style={[
        styles.transactionCard,
        isLinked && styles.linkedTransactionCard
      ]}>
        <View style={styles.transactionIconContainer}>
          <View style={[
            styles.iconBackground, 
            { backgroundColor: item.type === 'income' ? '#ecfdf5' : '#fff1f2' }
          ]}>
            <MaterialCommunityIcons 
              name={item.type === 'income' ? 'arrow-down-circle' : 'arrow-up-circle'} 
              size={24} 
              color={item.type === 'income' ? '#10b981' : '#f43f5e'} 
            />
          </View>
        </View>
        
        <View style={styles.transactionDetails}>
          <View style={styles.transactionTitleRow}>
            <ThemedText style={styles.transactionDescription}>
              {item.description}
            </ThemedText>
            <View style={[
              styles.transactionTypeBadge,
              { backgroundColor: item.type === 'income' ? '#10b981' : '#f43f5e' }
            ]}>
              <ThemedText style={styles.badgeText}>
                {item.type === 'income' ? 'Received' : 'Spent'}
              </ThemedText>
            </View>
          </View>
          <ThemedText style={styles.transactionSubtext}>
            {item.type === 'income' 
              ? `From: ${item.paid_to_received_from}` 
              : `To: ${item.paid_to_received_from}`}
          </ThemedText>
          <ThemedText style={styles.transactionDate}>
            {formatDate(item.transaction_date)}
          </ThemedText>
        </View>
        
        <View style={styles.transactionAmount}>
          <ThemedText style={[
            styles.amountText, 
            { color: item.type === 'income' ? '#10b981' : '#f43f5e' }
          ]}>
            {item.type === 'income' ? '+' : '-'} ₹{parseFloat(item.amount.toString()).toFixed(2)}
          </ThemedText>
        </View>
      </ThemedView>
    );
  };

  // Main render function
  const renderMainContent = () => {
    // Show loading while checking subscription
    if (subscriptionLoading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={primaryColor} />
          <ThemedText style={styles.loadingText}>Checking subscription...</ThemedText>
        </View>
      );
    }

    // Show access denied if user doesn't have subscription access
    if (!isSubscribed()) {
      return renderAccessDenied();
    }

    // Show main content if user has access
    return renderTransactionsContent();
  };

  const renderTransactionsContent = () => {
    // ... existing transaction rendering logic ...
    return (
      <View style={styles.container}>
        {/* Header */}
        <View style={[styles.header, { paddingTop: 10 }]}>
          <ThemedText style={styles.headerTitle}>Transactions</ThemedText>
          <TouchableOpacity
            style={[styles.headerAddButton, { backgroundColor: primaryColor }]}
            onPress={() => router.push('/transaction-form')}
          >
            <Ionicons name="add" size={24} color="white" />
          </TouchableOpacity>
        </View>

        {/* Summary Cards */}
        <View style={styles.summaryContainer}>
          <View style={[styles.summaryCard, { backgroundColor: colorScheme === 'dark' ? '#1f2937' : '#f3f4f6' }]}>
            <MaterialCommunityIcons name="arrow-up" size={20} color="#10b981" />
            <ThemedText style={styles.summaryLabel}>Income</ThemedText>
            <ThemedText style={[styles.summaryAmount, { color: '#10b981' }]}>
              ₹{summary.totalIncome.toLocaleString()}
            </ThemedText>
          </View>

          <View style={[styles.summaryCard, { backgroundColor: colorScheme === 'dark' ? '#1f2937' : '#f3f4f6' }]}>
            <MaterialCommunityIcons name="arrow-down" size={20} color="#ef4444" />
            <ThemedText style={styles.summaryLabel}>Expense</ThemedText>
            <ThemedText style={[styles.summaryAmount, { color: '#ef4444' }]}>
              ₹{summary.totalExpense.toLocaleString()}
            </ThemedText>
          </View>

          <View style={[styles.summaryCard, { backgroundColor: colorScheme === 'dark' ? '#1f2937' : '#f3f4f6' }]}>
            <MaterialCommunityIcons 
              name={summary.balance >= 0 ? "trending-up" : "trending-down"} 
              size={20} 
              color={summary.balance >= 0 ? '#10b981' : '#ef4444'} 
            />
            <ThemedText style={styles.summaryLabel}>Balance</ThemedText>
            <ThemedText style={[
              styles.summaryAmount, 
              { color: summary.balance >= 0 ? '#10b981' : '#ef4444' }
            ]}>
              ₹{Math.abs(summary.balance).toLocaleString()}
            </ThemedText>
          </View>
        </View>

        {/* Filter and Sort Controls */}
        <View style={styles.controlsContainer}>
          <View style={styles.filterContainer}>
            {(['all', 'income', 'expense'] as FilterType[]).map((filterType) => (
              <TouchableOpacity
                key={filterType}
                style={[
                  styles.filterButton,
                  filter === filterType && { backgroundColor: primaryColor },
                  { borderColor: primaryColor }
                ]}
                onPress={() => setFilter(filterType)}
              >
                <ThemedText style={[
                  styles.filterText,
                  filter === filterType && { color: 'white' }
                ]}>
                  {filterType.charAt(0).toUpperCase() + filterType.slice(1)}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </View>

          <TouchableOpacity
            style={[styles.sortButton, { borderColor: primaryColor }]}
            onPress={() => {
              const sortOptions: SortOption[] = ['newest', 'oldest', 'highest', 'lowest'];
              const currentIndex = sortOptions.indexOf(sortBy);
              const nextIndex = (currentIndex + 1) % sortOptions.length;
              setSortBy(sortOptions[nextIndex]);
            }}
          >
            <Ionicons name="swap-vertical" size={16} color={primaryColor} />
            <ThemedText style={[styles.sortText, { color: primaryColor }]}>
              {sortBy.charAt(0).toUpperCase() + sortBy.slice(1)}
            </ThemedText>
          </TouchableOpacity>
        </View>

        {/* Transactions List */}
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={primaryColor} />
            <ThemedText style={styles.loadingText}>Loading transactions...</ThemedText>
          </View>
        ) : getFilteredAndSortedTransactions().length === 0 ? (
          <View style={styles.emptyContainer}>
            <MaterialCommunityIcons 
              name="receipt" 
              size={80} 
              color={colorScheme === 'dark' ? '#6b7280' : '#9ca3af'} 
            />
            <ThemedText style={styles.emptyTitle}>No transactions yet</ThemedText>
            <ThemedText style={styles.emptyText}>
              Add your first transaction to start tracking
            </ThemedText>
            <TouchableOpacity
              style={[styles.createFirstButton, { backgroundColor: primaryColor }]}
              onPress={() => router.push('/transaction-form')}
            >
              <ThemedText style={[styles.createFirstButtonText, { color: 'white' }]}>
                Add Transaction
              </ThemedText>
            </TouchableOpacity>
          </View>
        ) : (
          <FlatList
            data={getFilteredAndSortedTransactions()}
            renderItem={renderTransaction}
            keyExtractor={(item) => item.id}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
          />
        )}
      </View>
    );
  };

  return (
    <ThemedView style={[styles.container, { paddingTop: insets.top }]}>
      {renderMainContent()}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  mainContent: {
    flex: 1,
  },
  blurOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  blurView: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  lockIconContainer: {
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    padding: 30,
    borderRadius: 20,
    marginHorizontal: 20,
  },
  lockText: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 16,
    color: '#f97316',
  },
  lockSubText: {
    fontSize: 16,
    opacity: 0.8,
    marginTop: 8,
    textAlign: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  headerAddButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  summaryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  summaryCard: {
    flex: 1,
    borderRadius: 12,
    padding: 12,
    marginHorizontal: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  summaryLabel: {
    fontSize: 12,
    color: '#000000',
    marginBottom: 4,
  },
  summaryAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  filterSortContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  filterContainer: {
    flexDirection: 'row',
  },
  filterButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  filterText: {
    fontWeight: '500',
    fontSize: 14,
  },
  sortDropdown: {
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
  },
  sortButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  sortButtonText: {
    fontWeight: '500',
    fontSize: 14,
    marginRight: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    marginTop: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '500',
    marginTop: 12,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    opacity: 0.7,
    textAlign: 'center',
  },
  createFirstButton: {
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    alignItems: 'center',
  },
  createFirstButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  listContainer: {
    paddingBottom: 80, // Extra space for FAB
    minHeight: '100%',
  },
  transactionCard: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  linkedTransactionCard: {
    borderLeftWidth: 3,
    borderLeftColor: '#e5e7eb',
  },
  transactionIconContainer: {
    marginRight: 12,
  },
  iconBackground: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  transactionDetails: {
    flex: 1,
  },
  transactionTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  transactionDescription: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  transactionTypeBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  badgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: 'white',
  },
  transactionSubtext: {
    fontSize: 14,
    opacity: 0.7,
    marginBottom: 2,
  },
  transactionDate: {
    fontSize: 12,
    opacity: 0.5,
  },
  transactionAmount: {
    justifyContent: 'center',
    alignItems: 'flex-end',
  },
  amountText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  addButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
  },
  accessDeniedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  accessDeniedTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 12,
    textAlign: 'center',
  },
  accessDeniedText: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  subscribeButton: {
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    alignItems: 'center',
  },
  subscribeButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sortText: {
    fontWeight: '500',
    fontSize: 14,
    marginLeft: 4,
  },
}); 